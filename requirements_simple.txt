# FastAPI and ASGI server
fastapi==0.115.12
uvicorn[standard]==0.34.0
pydantic==2.10.4
pydantic-settings==2.10.1

# Core ML and NLP dependencies
pandas==2.2.3
pytextrank==3.3.0
sentence-transformers==3.3.1
spacy==3.8.3
en_core_web_md @ https://github.com/explosion/spacy-models/releases/download/en_core_web_md-3.8.0/en_core_web_md-3.8.0-py3-none-any.whl
openai==1.59.5

# Vector database (with fallback for Windows)
pymilvus[model]==2.5.3

# Utilities
python-dotenv==1.0.1
numpy==2.2.6

# Optional: Keep gunicorn for production deployment if needed
# gunicorn==23.0.0