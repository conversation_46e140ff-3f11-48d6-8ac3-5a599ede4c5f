from typing import List, Dict, Optional
from math import ceil
import os
import platform
import numpy as np
from sentence_transformers import SentenceTransformer

from .csv_converter import csv_to_json

class MockEmbeddingManager:
    """Mock embedding manager for Windows/development environments where <PERSON><PERSON><PERSON>s Lite is not available."""

    def __init__(self, embedding_model: str = "all-MiniLM-L6-v2", openai_api_key: Optional[str] = None):
        """Initialize the mock embedding manager."""
        self.embedding_model = embedding_model
        self.openai_api_key = openai_api_key
        self.collections = {}
        self.data_store = {}

        # Initialize sentence transformer for embeddings
        try:
            self.sentence_transformer = SentenceTransformer(embedding_model)
            print(f"Initialized SentenceTransformer with model: {embedding_model}")
        except Exception as e:
            print(f"Warning: Could not initialize SentenceTransformer: {e}")
            self.sentence_transformer = None

    def create_collection(self, collection_name: str = "default"):
        """Mock collection creation."""
        if collection_name in self.collections:
            print(f"Collection '{collection_name}' already exists.")
            return "exists"

        self.collections[collection_name] = True
        self.data_store[collection_name] = []
        print(f"Mock collection '{collection_name}' created successfully.")
        return "success"

    def upsert_data(self, collection_name: str, data: List[Dict]):
        """Mock data insertion."""
        if collection_name not in self.collections:
            self.create_collection(collection_name)

        # Generate embeddings for the data
        if self.sentence_transformer:
            texts = [item["name"] for item in data]
            embeddings = self.sentence_transformer.encode(texts)

            for i, item in enumerate(data):
                item_with_embedding = item.copy()
                item_with_embedding["embedding"] = embeddings[i].tolist()
                self.data_store[collection_name].append(item_with_embedding)
        else:
            # Fallback: store without embeddings
            self.data_store[collection_name].extend(data)

        print(f"Mock upserted {len(data)} records into collection '{collection_name}'.")

    def load_collection(self, collection_name: str, load_fields=None, skip_load_dynamic_field=True):
        """Mock collection loading."""
        print(f"Mock loaded collection '{collection_name}'.")

    def search(self, collection_name: str, query_texts: List[str], top_k: int = 3,
               simple_output: bool = True, filter: Optional[str] = None,
               output_fields: Optional[List[str]] = None, search_params: Optional[Dict] = None):
        """Mock similarity search."""
        if collection_name not in self.data_store:
            return []

        if not self.sentence_transformer:
            # Return first few items as fallback
            items = self.data_store[collection_name][:top_k]
            if simple_output:
                return [item.get("name", "") for item in items]
            return items

        # Generate embeddings for query texts
        query_embeddings = self.sentence_transformer.encode(query_texts)

        results = []
        for query_embedding in query_embeddings:
            similarities = []
            for item in self.data_store[collection_name]:
                if "embedding" in item:
                    # Calculate cosine similarity
                    item_embedding = np.array(item["embedding"])
                    similarity = np.dot(query_embedding, item_embedding) / (
                        np.linalg.norm(query_embedding) * np.linalg.norm(item_embedding)
                    )
                    similarities.append((similarity, item))

            # Sort by similarity and take top_k
            similarities.sort(key=lambda x: x[0], reverse=True)
            top_items = [item for _, item in similarities[:top_k]]
            results.extend(top_items)

        if simple_output:
            return list(set([item.get("name", "") for item in results]))

        return results

    def query(self, collection_name, filter, output_fields):
        """Mock query operation."""
        if collection_name not in self.data_store:
            return []
        return self.data_store[collection_name]


class ChromaEmbeddingManager:
    """ChromaDB-based embedding manager for Windows environments."""

    def __init__(self, embedding_model: str = "all-MiniLM-L6-v2", openai_api_key: Optional[str] = None, persist_directory: str = "./chroma_db"):
        """Initialize the ChromaDB embedding manager."""
        self.embedding_model = embedding_model
        self.openai_api_key = openai_api_key
        self.persist_directory = persist_directory

        # Initialize ChromaDB client
        try:
            import chromadb
            from chromadb.config import Settings

            # Create persistent client
            self.chroma_client = chromadb.PersistentClient(
                path=persist_directory,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            print(f"Initialized ChromaDB client with persist directory: {persist_directory}")
        except Exception as e:
            print(f"Error initializing ChromaDB client: {e}")
            raise

        # Initialize sentence transformer for embeddings
        try:
            self.sentence_transformer = SentenceTransformer(embedding_model)
            print(f"Initialized SentenceTransformer with model: {embedding_model}")
        except Exception as e:
            print(f"Warning: Could not initialize SentenceTransformer: {e}")
            self.sentence_transformer = None

    def create_collection(self, collection_name: str = "default"):
        """Create a ChromaDB collection."""
        try:
            # Check if collection already exists
            existing_collections = [col.name for col in self.chroma_client.list_collections()]
            if collection_name in existing_collections:
                print(f"Collection '{collection_name}' already exists.")
                return "exists"

            # Create new collection with cosine similarity
            self.collection = self.chroma_client.create_collection(
                name=collection_name,
                metadata={"hnsw:space": "cosine"}
            )
            print(f"ChromaDB collection '{collection_name}' created successfully.")
            return "success"
        except Exception as e:
            print(f"Error creating collection '{collection_name}': {e}")
            return "error"

    def upsert_data(self, collection_name: str, data: List[Dict]):
        """Insert data into the ChromaDB collection."""
        try:
            # Get or create collection
            try:
                collection = self.chroma_client.get_collection(collection_name)
            except Exception:
                # Collection doesn't exist, create it
                self.create_collection(collection_name)
                collection = self.chroma_client.get_collection(collection_name)

            if not self.sentence_transformer:
                print("Warning: No sentence transformer available, skipping embedding generation")
                return

            # Extract texts and generate embeddings
            texts = [item["name"] for item in data]
            embeddings = self.sentence_transformer.encode(texts).tolist()

            # Prepare data for ChromaDB
            ids = [str(item.get("label_id", i)) for i, item in enumerate(data)]
            documents = texts
            metadatas = []

            for item in data:
                metadata = {}
                for key, value in item.items():
                    if key not in ["name", "label_id", "embedding"]:
                        # ChromaDB metadata values must be strings, numbers, or booleans
                        if value is not None:
                            metadata[key] = str(value) if not isinstance(value, (int, float, bool)) else value
                metadatas.append(metadata)

            # Upsert data to ChromaDB
            collection.upsert(
                ids=ids,
                embeddings=embeddings,
                documents=documents,
                metadatas=metadatas
            )

            print(f"ChromaDB upserted {len(data)} records into collection '{collection_name}'.")

        except Exception as e:
            print(f"Error upserting data to collection '{collection_name}': {e}")

    def load_collection(self, collection_name: str, load_fields=None, skip_load_dynamic_field=True):
        """Load the specified ChromaDB collection (no-op for ChromaDB as it's always loaded)."""
        try:
            collection = self.chroma_client.get_collection(collection_name)
            print(f"ChromaDB collection '{collection_name}' loaded (ready for queries).")
        except Exception as e:
            print(f"Error loading collection '{collection_name}': {e}")

    def search(self, collection_name: str, query_texts: List[str], top_k: int = 3,
               simple_output: bool = True, filter: Optional[str] = None,
               output_fields: Optional[List[str]] = None, search_params: Optional[Dict] = None):
        """Search for similar texts in the ChromaDB collection."""
        try:
            collection = self.chroma_client.get_collection(collection_name)

            if not self.sentence_transformer:
                print("Warning: No sentence transformer available for search")
                return []

            # Generate embeddings for query texts
            query_embeddings = self.sentence_transformer.encode(query_texts).tolist()

            # Convert Milvus-style filter to ChromaDB where clause if provided
            where_clause = None
            if filter:
                # Simple conversion for basic filters like 'category == "Concept"'
                # This is a basic implementation - more complex filters would need more sophisticated parsing
                if "==" in filter:
                    parts = filter.split("==")
                    if len(parts) == 2:
                        field = parts[0].strip()
                        value = parts[1].strip().strip('"\'')
                        where_clause = {field: value}

            # Perform search
            results = collection.query(
                query_embeddings=query_embeddings,
                n_results=top_k,
                where=where_clause
            )

            if simple_output:
                # Return unique document names
                unique_names = set()
                if results and 'documents' in results:
                    for doc_list in results['documents']:
                        for doc in doc_list:
                            unique_names.add(doc)
                return list(unique_names)
            else:
                # Return results in a format similar to Milvus
                formatted_results = []
                if results and 'documents' in results:
                    for i, doc_list in enumerate(results['documents']):
                        for j, doc in enumerate(doc_list):
                            result_item = {
                                'entity': {
                                    'name': doc
                                }
                            }
                            # Add metadata if available
                            if 'metadatas' in results and i < len(results['metadatas']) and j < len(results['metadatas'][i]):
                                metadata = results['metadatas'][i][j]
                                if metadata:
                                    result_item['entity'].update(metadata)

                            # Add distance/score if available
                            if 'distances' in results and i < len(results['distances']) and j < len(results['distances'][i]):
                                result_item['distance'] = results['distances'][i][j]

                            formatted_results.append(result_item)

                return [formatted_results]  # Wrap in list to match Milvus format

        except Exception as e:
            print(f"Error searching collection '{collection_name}': {e}")
            return []

    def query(self, collection_name, filter, output_fields):
        """Query operation for ChromaDB."""
        try:
            collection = self.chroma_client.get_collection(collection_name)

            # Convert filter to where clause
            where_clause = None
            if filter:
                if "==" in filter:
                    parts = filter.split("==")
                    if len(parts) == 2:
                        field = parts[0].strip()
                        value = parts[1].strip().strip('"\'')
                        where_clause = {field: value}

            # Get all results matching the filter
            results = collection.get(where=where_clause)

            # Format results to match expected structure
            formatted_results = []
            if results and 'documents' in results:
                for i, doc in enumerate(results['documents']):
                    result_item = {'name': doc}

                    # Add metadata
                    if 'metadatas' in results and i < len(results['metadatas']):
                        metadata = results['metadatas'][i]
                        if metadata:
                            result_item.update(metadata)

                    # Add ID as label_id
                    if 'ids' in results and i < len(results['ids']):
                        try:
                            result_item['label_id'] = int(results['ids'][i])
                        except ValueError:
                            result_item['label_id'] = results['ids'][i]

                    formatted_results.append(result_item)

            return formatted_results

        except Exception as e:
            print(f"Error querying collection '{collection_name}': {e}")
            return []

    def delete_collection(self, collection_name: str):
        """Delete a ChromaDB collection."""
        try:
            self.chroma_client.delete_collection(collection_name)
            print(f"ChromaDB collection '{collection_name}' deleted successfully.")
        except Exception as e:
            print(f"Error deleting collection '{collection_name}': {e}")


class EmbeddingManager:
    def __init__(self, milvus_uri: str = "./milvus.db", embedding_model: str = "all-MiniLM-L6-v2",
                 openai_api_key: Optional[str] = None):
        """
        Initialize the EmbeddingManager.

        :param milvus_uri: Milvus uri.
        :param embedding_model: Name of the Sentence Transformer model or 'openai' for OpenAI embeddings.
        :param openai_api_key: OpenAI API key if using OpenAI embeddings.
        """
        self.embedding_model = embedding_model
        self.openai_api_key = openai_api_key

        # Check if we're on Windows or if Milvus Lite is not available
        if platform.system() == "Windows":
            print("Windows detected. Using ChromaDB embedding manager instead of Milvus.")
            self._use_chroma = True
            self.chroma_manager = ChromaEmbeddingManager(embedding_model, openai_api_key)
            return

        try:
            from pymilvus import MilvusClient, DataType, model
            self.milvus_client = MilvusClient(uri=milvus_uri)
            self._use_chroma = False

            if self.embedding_model.startswith("text-embedding") and self.openai_api_key:
                self.embedding_ef = model.dense.OpenAIEmbeddingFunction(
                    model_name=self.embedding_model, # Specify the model name
                    api_key=self.openai_api_key, # Provide your OpenAI API key
                    dimensions=1536 # Set the embedding dimensionality
                )
            else:
                # print("OpenAI API key not provided. Using SentenceTransformer embeddings.")
                self.embedding_ef = model.dense.SentenceTransformerEmbeddingFunction(
                    model_name=self.embedding_model, # Specify the model name
                    device='cpu' # Specify the device to use, e.g., 'cpu' or 'cuda:0'
                )
        except Exception as e:
            print(f"Could not initialize Milvus client: {e}")
            print("Falling back to ChromaDB embedding manager.")
            self._use_chroma = True
            self.chroma_manager = ChromaEmbeddingManager(embedding_model, openai_api_key)


    def create_collection(self, collection_name: str = "default"):
        """
        Create a Milvus collection with dynamic schema based on the embedding dimension.

        :param collection_name: Name of the collection.
        """
        if self._use_chroma:
            return self.chroma_manager.create_collection(collection_name)

        if self.milvus_client.has_collection(collection_name):
            print(f"Collection '{collection_name}' already exists.")
            return "exists"

        # Calculate embedding dimension dynamically
        embedding_dimension = len(self.embedding_ef(['sample_text'])[0])

        # Define the schema dynamically
        from pymilvus import DataType
        schema = self.milvus_client.create_schema(
            enable_dynamic_field=True,
        )
        schema.add_field(field_name="label_id", datatype=DataType.INT64, is_primary=True)
        schema.add_field(field_name="name", datatype=DataType.VARCHAR, max_length=512)
        schema.add_field(field_name="embedding", datatype=DataType.FLOAT_VECTOR, dim=embedding_dimension)
        schema.add_field(field_name="category", datatype=DataType.VARCHAR, default_value="general", max_length=512)

        index_params = self.milvus_client.prepare_index_params()
        index_params.add_index(
            field_name="embedding",
            index_type="AUTOINDEX",
            metric_type="COSINE"
        )

        self.milvus_client.create_collection(collection_name=collection_name, schema=schema, index_params=index_params)
        print(f"Collection '{collection_name}' created successfully with embedding dimension {embedding_dimension}.")
        return "success"

    def upsert_data(self, collection_name: str, data: List[Dict]):
        """
        Insert data into the Milvus collection dynamically in batches.

        :param collection_name: Name of the collection.
        :param data: List of dictionaries containing the data.
        """
        if self._use_chroma:
            return self.chroma_manager.upsert_data(collection_name, data)

        batch_size = 10000

        # Split data into batches
        num_batches = ceil(len(data) / batch_size)

        for batch_index in range(num_batches):
            # Get the current batch of data
            start_idx = batch_index * batch_size
            end_idx = min((batch_index + 1) * batch_size, len(data))
            batch_data = data[start_idx:end_idx]

            # Extract texts and generate embeddings for the current batch
            texts = [item["name"] for item in batch_data]
            embeddings = self.embedding_ef(texts)

            # Add embeddings to the batch data
            for index, item in enumerate(batch_data):
                item["embedding"] = embeddings[index]

            # Perform upsert for the current batch
            self.milvus_client.upsert(collection_name=collection_name, data=batch_data)
            print(f"Upserted {len(batch_data)} records into collection '{collection_name}' (Batch {batch_index + 1}/{num_batches}).")

        print(self.milvus_client.describe_collection(collection_name=collection_name))


    def load_collection(self, collection_name: str, load_fields=["label_id", "name", "embedding", "category"], skip_load_dynamic_field=True):
        """
        Load the specified Milvus collection.

        :param collection_name: Name of the collection.
        """
        if self._use_chroma:
            return self.chroma_manager.load_collection(collection_name, load_fields, skip_load_dynamic_field)

        self.milvus_client.load_collection(collection_name=collection_name, load_fields=load_fields, skip_load_dynamic_field=skip_load_dynamic_field)

    def search(self, collection_name: str, query_texts: List[str], top_k: int = 3, simple_output: bool = True, filter: Optional[str] = None, output_fields: Optional[List[str]] = ["name"], search_params: Optional[Dict] = {"metric_type": "COSINE"}):
        """
        Search for similar texts in the Milvus collection.

        :param collection_name: Name of the collection.
        :param query_texts: a list of texts to search for.
        :param top_k: Number of top results to return.
        :param simple_output: If True, return a simplified list of unique labels. The output_fields argument may be suppressed but must at least has "name". If not, the original search results from milvus_client are returned.
        :param filter: Optional filter expression.
        :param output_fields: Optional list of fields to return. The primary field will be returned if not set.
        :return: List of search results.
        """
        if self._use_chroma:
            return self.chroma_manager.search(collection_name, query_texts, top_k, simple_output, filter, output_fields, search_params)

        query_embeddings = self.embedding_ef(query_texts)
        # output_fields = ["Record_Type"] if output_fields is None else output_fields

        results = self.milvus_client.search(collection_name=collection_name, data=query_embeddings, limit=top_k, filter=filter, output_fields=output_fields, search_params=search_params)

        if simple_output:
            label_set = set()
            for sublist in results:
                for item in sublist:
                    if 'entity' in item and 'name' in item['entity']:
                        label_set.add(item['entity']['name'])
            return list(label_set)
        else:
            return results

    def query(self, collection_name, filter, output_fields):
        if self._use_chroma:
            return self.chroma_manager.query(collection_name, filter, output_fields)

        return self.milvus_client.query(collection_name=collection_name, filter=filter, output_fields=output_fields)

    def delete_collection(self, collection_name: str):
        """
        Delete a Milvus or ChromaDB collection.

        :param collection_name: Name of the collection.
        """
        if self._use_chroma:
            return self.chroma_manager.delete_collection(collection_name)

        if self.milvus_client.has_collection(collection_name):
            self.milvus_client.drop_collection(collection_name)
            print(f"Collection '{collection_name}' deleted successfully.")
        else:
            print(f"Collection '{collection_name}' does not exist.")


# Example usage
if __name__ == "__main__":
    # Initialize the EmbeddingManager
    embedding_manager = EmbeddingManager(embedding_model="all-MiniLM-L6-v2")

    current_directory = os.path.dirname(os.path.abspath(__file__))
    label_pool_path = os.path.join(current_directory, 'label_pool.csv')
    has_header_input = input("Does the CSV file have a header? (yes/no): ").strip().lower()
    has_header = has_header_input == 'yes'
    json_data = csv_to_json(label_pool_path, delimiter='#', has_header=has_header)
    # print("JSON Data:\n", json_data)

    # Create a collection and upsert data
    res = embedding_manager.create_collection("test_collection")
    if res == "success":
        embedding_manager.upsert_data("test_collection", json_data)

    # Search
    results = embedding_manager.search("test_collection", ["industrial revolution", "Japanese empire"], top_k=3, simple_output=True, filter='category == "Concept"', search_params={"metric_type": "COSINE", "params":{"radius": 0.2}})
    
    print("Search Results:", results)

    # # Delete the collection
    # embedding_manager.delete_collection("test_collection")